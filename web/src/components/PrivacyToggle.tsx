import React from "react";
import { FormikProps } from "formik";
import { Switch } from "@/components/ui/switch";
import { Label, SubLabel } from "@/components/admin/connectors/Field";
import { SearchMultiSelectDropdown, Option as DropdownOption } from "@/components/Dropdown";
import { SourceChip } from "@/app/chat/input/ChatInputBar";
import { GroupsIconSkeleton } from "@/components/icons/icons";
import { UserTeams } from "@/lib/types";
import { PopupSpec } from "@/components/admin/connectors/Popup";

interface PrivacyToggleFormValues {
  is_public: boolean;
  selectedTeams: number[];
}

interface PrivacyToggleProps<T extends PrivacyToggleFormValues> {
  formikProps: FormikProps<T>;
  userTeams?: UserTeams[];
  objectName?: string;
  setPopup?: (popup: PopupSpec) => void;
  className?: string;
  singleTeamMode?: boolean;
  onTeamsChange?: (newTeams: number[]) => void;
}

export function PrivacyToggle<T extends PrivacyToggleFormValues>({
  formikProps,
  userTeams = [],
  objectName = "item",
  setPopup,
  className = "",
  singleTeamMode = false,
  onTeamsChange,
}: PrivacyToggleProps<T>) {
  const handleToggleChange = (checked: boolean) => {
    formikProps.setFieldValue("is_public", checked);

    // Clear teams when switching to public
    if (checked) {
      formikProps.setFieldValue("selectedTeams", []);
      onTeamsChange?.([]);
    }
  };

  const handleTeamSelect = (selected: DropdownOption<string | number>) => {
    const option = selected as {
      name: string;
      value: string | number;
      type: "team";
    };

    if (option.type === "team") {
      let newTeams: number[];
      if (singleTeamMode) {
        // For single team mode, replace the entire array with just the new selection
        newTeams = [option.value as number];
        formikProps.setFieldValue("selectedTeams", newTeams);
      } else {
        // For multi-team mode, add to existing selections
        newTeams = [...formikProps.values.selectedTeams, option.value as number];
        formikProps.setFieldValue("selectedTeams", newTeams);
      }
      onTeamsChange?.(newTeams);
    }
  };

  const handleTeamRemove = (teamId: number) => {
    const newTeams = formikProps.values.selectedTeams.filter(
      (id: number) => id !== teamId
    );
    formikProps.setFieldValue("selectedTeams", newTeams);
    onTeamsChange?.(newTeams);

    // Show popup if removing the last team while private
    if (!formikProps.values.is_public && newTeams.length === 0 && setPopup) {
      const message = singleTeamMode
        ? `Exactly one team must be selected when ${objectName} is private`
        : `At least one team must be selected when ${objectName} is private`;
      setPopup({
        message,
        type: "warning",
      });
    }
  };

  const validateTeamSelection = () => {
    if (!formikProps.values.is_public && formikProps.values.selectedTeams.length === 0) {
      if (setPopup) {
        const message = singleTeamMode
          ? `Exactly one team must be selected when ${objectName} is private`
          : `At least one team must be selected when ${objectName} is private`;
        setPopup({
          message,
          type: "error",
        });
      }
      return false;
    }
    return true;
  };

  // Note: Validation is handled through Formik validation schema

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Privacy Toggle */}
      <div className="flex items-center space-x-3">
        <Switch
          checked={formikProps.values.is_public}
          onCheckedChange={handleToggleChange}
          id="privacy-toggle"
        />
        <div className="flex flex-col">
          <Label className="text-sm font-medium">
            {formikProps.values.is_public ? "Public" : "Private"}
          </Label>
          <SubLabel>
            {formikProps.values.is_public
              ? `This ${objectName} will be available to all users`
              : `This ${objectName} will only be available to selected teams`}
          </SubLabel>
        </div>
      </div>

      {/* Team Selection (only shown when private) */}
      {!formikProps.values.is_public && (
        <div className="space-y-3">
          <div>
            <Label className="text-sm font-medium">
              {singleTeamMode ? "Assign to Team *" : "Share with Teams *"}
            </Label>
            <SubLabel>
              {singleTeamMode
                ? `Select exactly one team that will have access to this ${objectName}`
                : `Select at least one team to share this ${objectName} with`}
            </SubLabel>

          </div>
          
          <SearchMultiSelectDropdown
            options={userTeams
              .filter((t) => {
                if (singleTeamMode) {
                  // In single team mode, show all teams except the currently selected one
                  return !formikProps.values.selectedTeams.includes(t.id);
                } else {
                  // In multi-team mode, show all teams except already selected ones
                  return !formikProps.values.selectedTeams.includes(t.id);
                }
              })
              .map((t) => ({
                name: t.name,
                value: t.id,
                type: "team",
              }))}
            onSelect={handleTeamSelect}
          />
          
          {/* Selected Teams Display */}
          {formikProps.values.selectedTeams.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {formikProps.values.selectedTeams.map((teamId: number) => {
                const team = userTeams.find((t) => t.id === teamId);
                return team ? (
                  <SourceChip
                    key={team.id}
                    title={team.name}
                    onRemove={() => handleTeamRemove(team.id)}
                    icon={<GroupsIconSkeleton size={12} />}
                  />
                ) : null;
              })}
            </div>
          )}
          
          {/* Validation Error Display */}
          {formikProps.touched.selectedTeams &&
           formikProps.errors.selectedTeams && (
            <div className="text-red-500 text-sm">
              {String(formikProps.errors.selectedTeams)}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default PrivacyToggle;
