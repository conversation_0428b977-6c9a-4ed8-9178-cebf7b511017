import { type User } from "@/lib/types";
import { PopupSpec } from "@/components/admin/connectors/Popup";
import userMutationFetcher from "@/lib/admin/users/userMutationFetcher";
import useSWRMutation from "swr/mutation";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { ConfirmEntityModal } from "@/components/modals/ConfirmEntityModal";

const UninviteUserButton = ({
  user,
  setPopup,
  mutate,
  className,
  children,
}: {
  user: User;
  setPopup: (spec: PopupSpec) => void;
  mutate: () => void;
  className?: string;
  children?: React.ReactNode;
}) => {
  const { trigger, isMutating } = useSWRMutation(
    "/api/manage/admin/remove-invited-user",
    userMutationFetcher,
    {
      onSuccess: () => {
        mutate();
        setPopup({
          message: "User uninvited successfully!",
          type: "success",
        });
      },
      onError: (errorMsg) =>
        setPopup({
          message: `Unable to uninvite user - ${errorMsg.message}`,
          type: "error",
        }),
    }
  );

  const [showUninviteModal, setShowUninviteModal] = useState(false);
  return (
    <>
      {showUninviteModal && (
        <ConfirmEntityModal
          entityType="user invitation"
          entityName={user.email}
          onClose={() => setShowUninviteModal(false)}
          onSubmit={() => trigger({ user_email: user.email, method: "PATCH" })}
          additionalDetails="This will remove the user's invitation and prevent them from signing up."
        />
      )}

      <Button
        className={className}
        onClick={() => setShowUninviteModal(true)}
        disabled={isMutating}
        size="sm"
        variant="destructive"
      >
        {children}
      </Button>
    </>
  );
};

export default UninviteUserButton;
