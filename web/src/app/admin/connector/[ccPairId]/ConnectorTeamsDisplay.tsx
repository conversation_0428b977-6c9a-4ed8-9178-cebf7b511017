"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FiEdit2, FiUsers, FiGlobe } from "react-icons/fi";
import { FaSwatchbook } from "react-icons/fa";
import { useUserTeams } from "@/lib/hooks";
import { UserRole } from "@/lib/types";
import { useUser } from "@/components/user/UserProvider";
import { ConnectorTeamsEditor } from "./ConnectorTeamsEditor";
import { errorHandlingFetcher } from "@/lib/fetcher";
import useSWR from "swr";

interface ConnectorTeamsDisplayProps {
  ccPairId: number;
  isPublic: boolean;
  isEditable: boolean;
}

export function ConnectorTeamsDisplay({
  ccPairId,
  isPublic,
  isEditable,
}: ConnectorTeamsDisplayProps) {
  const { user } = useUser();
  const { data: userTeams } = useUserTeams();
  const [showEditor, setShowEditor] = useState(false);

  // Fetch current team associations
  const { data: currentTeams, error } = useSWR<number[]>(
    `/api/manage/admin/cc-pair/${ccPairId}/teams`,
    errorHandlingFetcher
  );

  // Only show for admin users
  if (!user || user.role !== UserRole.ADMIN) {
    return null;
  }

  if (error) {
    return (
      <div className="text-sm text-error">
        Failed to load team associations
      </div>
    );
  }

  if (!currentTeams) {
    return (
      <div className="text-sm text-text-500">
        Loading team associations...
      </div>
    );
  }

  // Get team names for display
  const teamNames = currentTeams
    .map((teamId) => userTeams?.find((team) => team.id === teamId)?.name)
    .filter(Boolean);

  if (showEditor) {
    return (
      <ConnectorTeamsEditor
        ccPairId={ccPairId}
        currentTeams={currentTeams}
        isPublic={isPublic}
        onClose={() => setShowEditor(false)}
      />
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Access</h4>
      </div>

      <div className="space-y-1">
        <div className="flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <span className="text-sm text-text-600">Access Type:</span>
          <span className="text-sm font-medium flex items-center gap-1">
            {isPublic ? (
              <>
                <FiGlobe className="h-3 w-3" />
                Public
              </>
            ) : (
              <>
                <FiUsers className="h-3 w-3" />
                Private
                {teamNames.length > 0 && (
                  <span className="text-text-500">
                    ({teamNames.join(", ")})
                  </span>
                )}
              </>
            )}
          </span>
        </div>
        
        {isEditable && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowEditor(true)}
            className="h-8 px-2 border border-border text-sm w-fit"
          >
            <FaSwatchbook className="mr-1" />
            Update Access
          </Button>
        )}
      </div>


        {!isPublic && teamNames.length === 0 && (
          <div className="text-sm text-text-500 italic">
            No teams assigned
          </div>
        )}
      </div>
    </div>
  );
}
