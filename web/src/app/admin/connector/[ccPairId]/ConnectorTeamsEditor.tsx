"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PrivacyToggle } from "@/components/PrivacyToggle";
import { usePopup } from "@/components/admin/connectors/Popup";
import { useUserTeams } from "@/lib/hooks";
import { UserRole } from "@/lib/types";
import { useUser } from "@/components/user/UserProvider";
import { Formik, FormikProps } from "formik";
import { mutate } from "swr";
import { buildCCPairInfoUrl } from "./lib";

interface ConnectorTeamsEditorProps {
  ccPairId: number;
  currentTeams: number[];
  isPublic: boolean;
  onClose: () => void;
}

interface FormValues {
  is_public: boolean;
  selectedTeams: number[];
}

export function ConnectorTeamsEditor({
  ccPairId,
  currentTeams,
  isPublic,
  onClose,
}: ConnectorTeamsEditorProps) {
  const { user } = useUser();
  const { data: userTeams } = useUserTeams();
  const { popup, setPopup } = usePopup();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Only show for admin users
  if (!user || user.role !== UserRole.ADMIN) {
    return null;
  }

  const initialValues: FormValues = {
    is_public: isPublic,
    selectedTeams: currentTeams,
  };

  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      // Update team associations
      const response = await fetch(`/api/manage/admin/cc-pair/${ccPairId}/teams`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values.is_public ? [] : values.selectedTeams),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to update team associations");
      }

      // Refresh the connector data
      await mutate(buildCCPairInfoUrl(ccPairId));

      setPopup({
        message: "Team associations updated successfully",
        type: "success",
      });

      onClose();
    } catch (error) {
      setPopup({
        message: `Failed to update team associations: ${error}`,
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="border border-border rounded-lg p-4 bg-background-subtle">
      {popup}
      <h4 className="text-md font-medium mb-4">Update Access</h4>

      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {(formikProps: FormikProps<FormValues>) => (
          <form onSubmit={formikProps.handleSubmit}>
            <div className="mb-4">
              <PrivacyToggle
                formikProps={formikProps}
                userTeams={userTeams || []}
                objectName="connector"
                setPopup={setPopup}
                singleTeamMode = {true}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                size="sm"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Updating..." : "Save Changes"}
              </Button>
            </div>
          </form>
        )}
      </Formik>
    </div>
  );
}
