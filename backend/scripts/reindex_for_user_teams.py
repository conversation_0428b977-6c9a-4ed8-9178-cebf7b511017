#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to trigger re-indexing of all document sets for user teams access control.

This script marks all document sets as needing re-indexing (is_up_to_date=False),
which will cause the background sync tasks to re-index all documents with the new
user_team_ids metadata field.

Usage:
    python backend/scripts/reindex_for_user_teams.py

This should be run after implementing the user teams access control feature
to ensure existing documents get the new metadata.
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from onyx.db.document_set import mark_all_document_sets_for_reindexing
from onyx.db.engine import get_session_context_manager
from onyx.utils.logger import setup_logger

logger = setup_logger()


def main():
    """Main function to trigger re-indexing for user teams access control."""
    logger.info("Starting re-indexing process for user teams access control...")
    
    try:
        with get_session_context_manager() as db_session:
            count = mark_all_document_sets_for_reindexing(db_session)
            logger.info(f"Successfully marked {count} document sets for re-indexing.")
            
        logger.info(
            "Re-indexing has been triggered. The background sync tasks will now "
            "process all documents and update them with user_team_ids metadata. "
            "This may take some time depending on the number of documents."
        )
        
    except Exception as e:
        logger.error(f"Failed to trigger re-indexing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
