"""match_any_keywords flag for standard answers

Revision ID: 5c7fdadae813
Revises: efb35676026c
Create Date: 2024-09-13 18:52:59.256478

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5c7fdadae813"
down_revision = "efb35676026c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "standard_answer",
        sa.Column(
            "match_any_keywords",
            sa.<PERSON>(),
            nullable=False,
            server_default=sa.false(),
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("standard_answer", "match_any_keywords")
    # ### end Alembic commands ###
