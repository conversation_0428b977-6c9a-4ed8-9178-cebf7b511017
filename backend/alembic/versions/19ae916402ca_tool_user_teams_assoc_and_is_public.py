"""
Revision ID: 19ae916402ca
Revises: 19ae916402c9
Create Date: 2024-06-07 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '19ae916402ca'
down_revision = '19ae916402c9'
branch_labels = None
depends_on = None

def upgrade():
    op.drop_table('tool__user_teams')
    op.create_table(
        'tool__user_teams',
        sa.Column('tool_id', sa.Integer(), sa.<PERSON>('tool.id'), primary_key=True),
        sa.<PERSON>umn('user_group_id', sa.Integer(), sa.<PERSON>('user_group.id'), primary_key=True),
    )
    op.add_column('tool', sa.Column('is_public', sa.<PERSON>(), nullable=False, server_default=sa.text('false')))

def downgrade():
    op.drop_column('tool', 'is_public')
    op.drop_table('tool__user_teams') 