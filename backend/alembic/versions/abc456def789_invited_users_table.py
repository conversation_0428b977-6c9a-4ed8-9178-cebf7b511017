"""Create invited_users table

Revision ID: abc456def789
Revises: 19ae916402c8
Create Date: 2024-07-15 12:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
import sqlalchemy.dialects.postgresql as psql
import uuid

# revision identifiers, used by Alembic.
revision = 'abc456def789'
down_revision = '2a1b3c4d5e6f'
branch_labels = None
depends_on = None

def upgrade():
    op.create_table(
        'invited_users',
        sa.Column('id', psql.UUID(as_uuid=True), sa.ForeignKey('user.id', ondelete='CASCADE'), primary_key=True),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('invited_at', sa.DateTime(), server_default=sa.func.now()),
    )

def downgrade():
    op.drop_table('invited_users')
