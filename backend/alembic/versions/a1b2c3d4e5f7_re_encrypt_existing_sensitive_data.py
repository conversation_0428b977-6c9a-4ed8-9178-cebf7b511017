"""re_encrypt_existing_sensitive_data

Revision ID: a1b2c3d4e5f7
Revises: a1b2c3d4e5f6
Create Date: 2025-08-21 10:51:44.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import json
from sqlalchemy.types import TypeEngine

# Import the encryption utilities from your project
from onyx.utils.encryption import encrypt_string_to_bytes, decrypt_bytes_to_string
from onyx.utils.logger import setup_logger

logger = setup_logger()

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f7"
down_revision = "a1b2c3d4e5f6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Encrypt existing plaintext data in sensitive columns.
    This migration runs after implementing encryption - existing data is plaintext
    stored as UTF-8 bytes in bytea columns, and needs to be encrypted.
    """
    connection = op.get_bind()

    def encrypt_table_column(
        table_name: str, 
        column_name: str, 
        pk_column_name: str, 
        pk_column_type: TypeEngine, 
        is_json: bool = False
    ):
        # Create table definition with LargeBinary since we know it's bytea
        table = sa.Table(
            table_name,
            sa.MetaData(),
            sa.Column(pk_column_name, pk_column_type, primary_key=True),
            sa.Column(column_name, sa.LargeBinary(), nullable=True),
        )

        logger.info(f"Processing {table_name}.{column_name} - Converting plaintext to encrypted")

        # Read existing plaintext data from bytea columns
        results = connection.execute(
            sa.select(getattr(table.c, pk_column_name), getattr(table.c, column_name))
            .where(getattr(table.c, column_name).is_not(None))
        ).fetchall()

        logger.info(f"Found {len(results)} rows with plaintext data to encrypt in {table_name}.{column_name}")

        for pk_value, current_data in results:
            if current_data is None:
                continue
                
            try:
                # Since you just implemented encryption, existing data should be plaintext bytes
                # (UTF-8 encoded strings stored in bytea columns)
                if not isinstance(current_data, (bytes, bytearray)):
                    logger.warning(f"Expected bytes but got {type(current_data)} in {table_name}.{column_name} PK {pk_value}")
                    # Try to convert to string anyway
                    plaintext_str = str(current_data)
                else:
                    # Decode the plaintext bytes to string
                    try:
                        plaintext_str = current_data.decode('utf-8')
                        logger.info(f"Decoded plaintext bytes in {table_name}.{column_name} PK {pk_value}")
                    except UnicodeDecodeError as e:
                        logger.error(f"Cannot decode plaintext bytes in {table_name}.{column_name} PK {pk_value}: {e}")
                        continue

                # For JSON columns, validate and normalize the JSON string
                if is_json:
                    try:
                        # Parse and re-serialize to ensure consistent formatting
                        json_obj = json.loads(plaintext_str)
                        plaintext_str = json.dumps(json_obj, separators=(',', ':'))
                        logger.info(f"Validated JSON data for {table_name}.{column_name} PK {pk_value}")
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"Invalid JSON in {table_name}.{column_name} PK {pk_value}: {e}, encrypting as-is")

                # Now encrypt the plaintext string
                encrypted_bytes = encrypt_string_to_bytes(plaintext_str)

                # Update the row with encrypted data
                connection.execute(
                    table.update()
                    .where(getattr(table.c, pk_column_name) == pk_value)
                    .values(**{column_name: encrypted_bytes})
                )
                
                logger.info(f"Successfully encrypted plaintext data in {table_name}.{column_name} PK: {pk_value}")
                
            except Exception as e:
                logger.error(f"Failed to encrypt {table_name}.{column_name} PK {pk_value}: {e}")
                continue

    # Process each table/column
    logger.info("Starting encryption of existing plaintext data...")
    
    # Encrypt credential_json in the 'credential' table (JSON)
    encrypt_table_column("credential", "credential_json", "id", sa.Integer(), is_json=True)

    # Encrypt api_key in the 'llm_provider' table (String)
    encrypt_table_column("llm_provider", "api_key", "id", sa.Integer())

    # Encrypt api_key in the 'embedding_provider' table (String)
    encrypt_table_column("embedding_provider", "api_key", "provider_type", sa.String())

    # Encrypt encrypted_value in the 'key_value_store' table (JSON)
    encrypt_table_column("key_value_store", "encrypted_value", "key", sa.String(), is_json=True)

    # Encrypt bot_token and app_token in the 'slack_bot' table (String)
    encrypt_table_column("slack_bot", "bot_token", "id", sa.Integer())
    encrypt_table_column("slack_bot", "app_token", "id", sa.Integer())
    
    logger.info("Plaintext encryption migration completed!")


def downgrade() -> None:
    """
    Decrypt encrypted data back to plaintext UTF-8 bytes.
    This restores the state before encryption was implemented.
    """
    connection = op.get_bind()

    def decrypt_to_plaintext(
        table_name: str,
        column_name: str,
        pk_column_name: str,
        pk_column_type: TypeEngine,
        is_json: bool = False,
    ) -> None:
        logger.info(f"Decrypting {table_name}.{column_name} back to plaintext bytes")
        
        # Use proper SQLAlchemy query for bytea columns
        table = sa.Table(
            table_name,
            sa.MetaData(),
            sa.Column(pk_column_name, pk_column_type, primary_key=True),
            sa.Column(column_name, sa.LargeBinary(), nullable=True),
        )
        
        results = connection.execute(
            sa.select(getattr(table.c, pk_column_name), getattr(table.c, column_name))
            .where(getattr(table.c, column_name).is_not(None))
        ).fetchall()

        logger.info(f"Found {len(results)} encrypted rows to decrypt in {table_name}.{column_name}")

        for pk_value, encrypted_data in results:
            if encrypted_data is None:
                continue
                
            try:
                # Data should be bytes in bytea columns
                if not isinstance(encrypted_data, (bytes, bytearray)):
                    logger.warning(f"Expected bytes but got {type(encrypted_data)} in {table_name}.{column_name} PK {pk_value}")
                    continue

                # Decrypt the encrypted bytes back to plaintext string
                plaintext_str = decrypt_bytes_to_string(encrypted_data)
                logger.info(f"Successfully decrypted {table_name}.{column_name} PK {pk_value}")

                # Convert back to the original plaintext format (UTF-8 bytes in bytea)
                if is_json:
                    # For JSON columns, keep as JSON string and encode to bytes
                    try:
                        # Ensure it's valid JSON first
                        json_obj = json.loads(plaintext_str)
                        json_str = json.dumps(json_obj, separators=(',', ':'))
                        plaintext_bytes = json_str.encode('utf-8')
                    except (json.JSONDecodeError, TypeError):
                        logger.warning(f"Invalid JSON after decryption for {table_name}.{column_name} PK {pk_value}")
                        plaintext_bytes = plaintext_str.encode('utf-8')
                else:
                    # For string columns, encode to UTF-8 bytes
                    plaintext_bytes = plaintext_str.encode('utf-8')

                # Update the row with plaintext bytes (restoring original format)
                connection.execute(
                    table.update()
                    .where(getattr(table.c, pk_column_name) == pk_value)
                    .values(**{column_name: plaintext_bytes})
                )
                
                logger.info(f"Successfully restored plaintext bytes for {table_name}.{column_name} PK: {pk_value}")
                
            except Exception as e:
                logger.error(f"Failed to decrypt {table_name}.{column_name} PK {pk_value}: {e}")
                continue

    logger.info("Starting decryption back to plaintext bytes...")
    
    # Decrypt all the columns back to their original plaintext byte format
    decrypt_to_plaintext("credential", "credential_json", "id", sa.Integer(), is_json=True)
    decrypt_to_plaintext("llm_provider", "api_key", "id", sa.Integer())
    decrypt_to_plaintext("embedding_provider", "api_key", "provider_type", sa.String())
    decrypt_to_plaintext("key_value_store", "encrypted_value", "key", sa.String(), is_json=True)
    decrypt_to_plaintext("slack_bot", "bot_token", "id", sa.Integer())
    decrypt_to_plaintext("slack_bot", "app_token", "id", sa.Integer())
    
    logger.info("Decryption to plaintext migration completed!")