"""Add TEAM_ADMIN role to userrole enum

Revision ID: 2a1b3c4d5e6f
Revises: 19ae916402cb
Create Date: 2024-06-10 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2a1b3c4d5e6f"
down_revision = "19ae916402cb"
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Use batch mode to modify the enum type
    with op.batch_alter_table("user", schema=None) as batch_op:
        batch_op.alter_column(
            "role",
            type_=sa.Enum(
                "BASIC",
                "ADMIN",
                "CURATOR",
                "GLOBAL_CURATOR",
                "TEAM_ADMIN",
                "EXT_PERM_USER",
                "SLACK_USER",
                "LIMITED",
                name="userrole",
                native_enum=False,
            ),
            existing_type=sa.Enum(
                "BASIC",
                "ADMIN",
                "CURATOR",
                "GLOBAL_CURATOR",
                "EXT_PERM_USER",
                "SLACK_USER",
                "LIMITED",
                name="userrole",
                native_enum=False,
            ),
            existing_nullable=False,
        )

def downgrade() -> None:
    # Update any TEAM_ADMIN users to BASIC before removing the value
    op.execute(
        "UPDATE \"user\" SET role = 'BASIC' WHERE role = 'TEAM_ADMIN'"
    )
    with op.batch_alter_table("user", schema=None) as batch_op:
        batch_op.alter_column(
            "role",
            type_=sa.Enum(
                "BASIC",
                "ADMIN",
                "CURATOR",
                "GLOBAL_CURATOR",
                "EXT_PERM_USER",
                "SLACK_USER",
                "LIMITED",
                name="userrole",
                native_enum=False,
            ),
            existing_type=sa.Enum(
                "BASIC",
                "ADMIN",
                "CURATOR",
                "GLOBAL_CURATOR",
                "TEAM_ADMIN",
                "EXT_PERM_USER",
                "SLACK_USER",
                "LIMITED",
                name="userrole",
                native_enum=False,
            ),
            existing_nullable=False,
        ) 