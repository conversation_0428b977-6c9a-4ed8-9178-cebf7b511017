"""Add display_sources column to persona table

Revision ID: 3fa9727197b7
Revises: c1d2e3f4g5h6
Create Date: 2023-05-14 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3fa9727197b7'
down_revision = 'c1d2e3f4g5h6'  # Latest migration at the time of creation
branch_labels = None
depends_on = None


def upgrade():
    # Add display_sources column to persona table with default value True
    op.add_column('persona', sa.Column('display_sources', sa.<PERSON>(), nullable=True, server_default='true'))


def downgrade():
    # Remove display_sources column from persona table
    op.drop_column('persona', 'display_sources')
