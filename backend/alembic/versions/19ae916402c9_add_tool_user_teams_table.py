"""
Revision ID: 19ae916402c9
Revises: 19ae916402c8
Create Date: 2024-06-07 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '19ae916402c9'
down_revision = '19ae916402c8'
branch_labels = None
depends_on = None

def upgrade():
    op.create_table(
        'tool__user_teams',
        sa.Column('user_group_id', sa.Integer(), sa.ForeignKey('user_group.id'), primary_key=True),
        sa.Column('tool_ids', postgresql.JSONB(astext_type=sa.Text()), nullable=False, server_default='[]'),
    )

def downgrade():
    op.drop_table('tool__user_teams') 
 