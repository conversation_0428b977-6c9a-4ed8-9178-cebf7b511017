"""Add user team default LLM provider table

Revision ID: a1b2c3d4e5f6
Revises: 20240322
Create Date: 2025-01-25 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f6"
down_revision = "20240322"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "user_team_default_llm_provider",
        sa.Column("user_group_id", sa.Integer(), nullable=False),
        sa.Column("llm_provider_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_group_id"],
            ["user_group.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["llm_provider_id"],
            ["llm_provider.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("user_group_id"),  # Each team gets only one default provider
    )

    op.create_index(
        "ix_user_team_default_llm_provider_user_group_id",
        "user_team_default_llm_provider",
        ["user_group_id"],
    )
    op.create_index(
        "ix_user_team_default_llm_provider_llm_provider_id",
        "user_team_default_llm_provider",
        ["llm_provider_id"],
    )

    # Populate defaults for teams that have specific LLM provider assignments
    # Only assign providers that are actually available to the team
    op.execute(
        """
        INSERT INTO user_team_default_llm_provider (user_group_id, llm_provider_id)
        SELECT DISTINCT ON (lpug.user_group_id)
               lpug.user_group_id,
               lpug.llm_provider_id
        FROM llm_provider__user_group lpug
        INNER JOIN llm_provider lp ON lpug.llm_provider_id = lp.id
        INNER JOIN user_group ug ON lpug.user_group_id = ug.id
        WHERE ug.is_up_to_date = true
          AND ug.is_up_for_deletion = false
          -- Only assign if provider is available to team (private + assigned OR public)
          AND (
              (lp.is_public = false AND lpug.llm_provider_id IS NOT NULL) OR
              lp.is_public = true
          )
        ORDER BY lpug.user_group_id,
                 -- Prefer team-specific private providers over public ones
                 CASE WHEN lp.is_public = false THEN 0 ELSE 1 END,
                 lpug.llm_provider_id
        """
    )

    # Ensure ALL active teams have a default LLM provider
    # For teams without specific assignments, use the first available provider
    # Priority: 1) Team-specific private providers, 2) Public providers
    op.execute(
        """
        INSERT INTO user_team_default_llm_provider (user_group_id, llm_provider_id)
        SELECT DISTINCT ON (ug.id)
               ug.id,
               COALESCE(team_provider.llm_provider_id, public_provider.id) as llm_provider_id
        FROM user_group ug
        -- Left join to get team-specific private providers
        LEFT JOIN (
            SELECT lpug.user_group_id,
                   MIN(lpug.llm_provider_id) as llm_provider_id
            FROM llm_provider__user_group lpug
            INNER JOIN llm_provider lp ON lpug.llm_provider_id = lp.id
            WHERE lp.is_public = false
            GROUP BY lpug.user_group_id
        ) team_provider ON ug.id = team_provider.user_group_id
        -- Cross join with first available public provider as fallback
        CROSS JOIN (
            SELECT MIN(id) as id
            FROM llm_provider
            WHERE is_public = true
        ) public_provider
        WHERE ug.is_up_to_date = true
          AND ug.is_up_for_deletion = false
          AND ug.id NOT IN (
              SELECT user_group_id
              FROM user_team_default_llm_provider
          )
          -- Only insert if we have at least one available provider
          AND (team_provider.llm_provider_id IS NOT NULL OR public_provider.id IS NOT NULL)
        ORDER BY ug.id
        """
    )


def downgrade() -> None:
    op.drop_index("ix_user_team_default_llm_provider_llm_provider_id")
    op.drop_index("ix_user_team_default_llm_provider_user_group_id")
    op.drop_table("user_team_default_llm_provider")
