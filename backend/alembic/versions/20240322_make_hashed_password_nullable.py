"""Make hashed_password nullable in user table

Revision ID: 20240322
Revises: abc456def789
Create Date: 2024-03-22 00:00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20240322'
down_revision = 'abc456def789'
branch_labels = None
depends_on = None

def upgrade():
    op.alter_column(
        'user',
        'hashed_password',
        existing_type=sa.String(length=1024),
        nullable=True,
    )

def downgrade():
    op.alter_column(
        'user',
        'hashed_password',
        existing_type=sa.String(length=1024),
        nullable=False,
    ) 