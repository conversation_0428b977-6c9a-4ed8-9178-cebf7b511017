"""
Revision ID: 19ae916402cb
Revises: 19ae916402ca
Create Date: 2024-06-07 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '19ae916402cb'
down_revision = '19ae916402ca'
branch_labels = None
depends_on = None

def upgrade():
    op.add_column('api_key', sa.<PERSON>umn('is_public', sa.<PERSON>(), nullable=False, server_default=sa.text('false')))
    op.create_table(
        'api_key__user_teams',
        sa.<PERSON>umn('api_key_id', sa.Integer(), sa.<PERSON>('api_key.id'), primary_key=True),
        sa.<PERSON>umn('user_group_id', sa.Integer(), sa.<PERSON>('user_group.id'), primary_key=True),
    )

def downgrade():
    op.drop_table('api_key__user_teams')
    op.drop_column('api_key', 'is_public') 