"""Add available_llm_models column to persona table

Revision ID: 19ae916402c8
Revises: 3fa9727197b7
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '19ae916402c8'
down_revision = '3fa9727197b7'
branch_labels = None
depends_on = None


def upgrade():
    # Add available_llm_models column to persona table
    op.add_column('persona', sa.Column('available_llm_models', postgresql.JSONB(), nullable=True))


def downgrade():
    # Remove available_llm_models column from persona table
    op.drop_column('persona', 'available_llm_models')
