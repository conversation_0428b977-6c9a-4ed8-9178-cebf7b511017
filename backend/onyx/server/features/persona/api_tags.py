from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import and_, func, select
from sqlalchemy.orm import Session

from onyx.auth.users import current_user
from onyx.configs.constants import DocumentSource
from onyx.db.engine import get_session
from onyx.db.models import (
    ConnectorCredentialPair,
    Document,
    DocumentByConnectorCredentialPair,
    DocumentSet,
    DocumentSet__ConnectorCredentialPair,
    Document__Tag,
    Persona,
    Persona__DocumentSet,
    Tag,
    User,
)
from onyx.db.persona import get_persona_by_id
from onyx.server.query_and_chat.models import SourceTag, TagResponse
from onyx.utils.logger import setup_logger

logger = setup_logger()

tags_router = APIRouter(prefix="/tags")


@tags_router.get("", response_model=TagResponse)
def get_tags_for_persona(
    persona_id: int = Query(..., description="The ID of the persona/assistant"),
    match_pattern: str | None = None,
    sources: list[DocumentSource] | None = None,
    limit: int = 50,
    user: User | None = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> TagResponse:
    """
    Get tags for a specific persona/assistant.
    Returns tags from documents in the document sets associated with the persona.
    """
    try:
        # Add debug logging
        logger.info(f"Getting tags for persona ID: {persona_id}")

        # Verify user has access to this persona
        try:
            # For regular users, we need to check if they have access to the persona
            # For admin users, we can just get the persona directly
            try:
                persona = get_persona_by_id(
                    persona_id=persona_id, user=user, db_session=db_session, is_for_edit=False
                )
                if not persona:
                    logger.error(f"Persona {persona_id} not found")
                    raise HTTPException(
                        status_code=404, detail=f"Persona {persona_id} not found"
                    )
                logger.info(f"Found persona: {persona.name} with {len(persona.document_sets)} document sets")
            except ValueError as ve:
                # If the user doesn't have access to the persona, try to get it directly
                # This is a fallback for cases where the user is using a public persona
                logger.warning(f"User doesn't have direct access to persona {persona_id}, trying public access: {str(ve)}")
                persona_stmt = select(Persona).where(Persona.id == persona_id, Persona.is_public == True)  # noqa: E712
                persona = db_session.execute(persona_stmt).scalar_one_or_none()
                if not persona:
                    logger.error(f"Persona {persona_id} not found or not public")
                    raise HTTPException(
                        status_code=404, detail=f"Persona {persona_id} not found or you don't have access to it"
                    )
                logger.info(f"Found public persona: {persona.name} with {len(persona.document_sets)} document sets")
        except Exception as e:
            logger.error(f"Error getting persona: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Error getting persona: {str(e)}"
            )

        # Get document set IDs associated with this persona
        document_set_ids = [ds.id for ds in persona.document_sets]
        logger.info(f"Document set IDs for persona {persona_id}: {document_set_ids}")

        # If the user is not an admin and there are no document sets, try to get public document sets
        if not document_set_ids and user and user.role != 'ADMIN':
            logger.info(f"No document sets found for persona {persona_id}, trying to get public document sets")
            # Get document sets that are public
            try:
                # First try to get document sets associated with the persona that are public
                persona_document_sets_stmt = select(DocumentSet).join(
                    Persona__DocumentSet, DocumentSet.id == Persona__DocumentSet.document_set_id
                ).where(
                    Persona__DocumentSet.persona_id == persona_id,
                    DocumentSet.is_public == True  # noqa: E712
                )
                persona_document_sets = db_session.execute(persona_document_sets_stmt).scalars().all()
                document_set_ids = [ds.id for ds in persona_document_sets]
                logger.info(f"Found {len(document_set_ids)} public document sets associated with persona {persona_id}")

                # If no document sets are found, try to get all public document sets
                if not document_set_ids:
                    public_document_sets_stmt = select(DocumentSet).where(DocumentSet.is_public == True)  # noqa: E712
                    public_document_sets = db_session.execute(public_document_sets_stmt).scalars().all()
                    document_set_ids = [ds.id for ds in public_document_sets]
                    logger.info(f"Found {len(document_set_ids)} public document sets in total")
            except Exception as e:
                logger.error(f"Error getting public document sets: {str(e)}")
                # Continue with empty document_set_ids

        if not document_set_ids:
            # If no document sets are associated with this persona, return empty list
            # We should not return all tags as that would expose tags from documents
            # the agent doesn't have access to
            logger.info(f"No document sets found for persona {persona_id}, returning empty tags list")
            return TagResponse(tags=[])

        # Build a query to get tags for documents in the specified document sets
        query = (
            select(Tag)
            .join(Document__Tag, Tag.id == Document__Tag.tag_id)
            .join(Document, Document.id == Document__Tag.document_id)
            .join(
                DocumentByConnectorCredentialPair,
                DocumentByConnectorCredentialPair.id == Document.id,
            )
            .join(
                ConnectorCredentialPair,
                and_(
                    ConnectorCredentialPair.connector_id
                    == DocumentByConnectorCredentialPair.connector_id,
                    ConnectorCredentialPair.credential_id
                    == DocumentByConnectorCredentialPair.credential_id,
                ),
            )
            .join(
                DocumentSet__ConnectorCredentialPair,
                DocumentSet__ConnectorCredentialPair.connector_credential_pair_id
                == ConnectorCredentialPair.id,
            )
            .where(
                DocumentSet__ConnectorCredentialPair.document_set_id.in_(document_set_ids),
                DocumentSet__ConnectorCredentialPair.is_current == True,  # noqa: E712
            )
        )
        # Apply filters if provided
        if match_pattern:
            query = query.where(
                func.lower(Tag.tag_key).contains(match_pattern.lower())
                | func.lower(Tag.tag_value).contains(match_pattern.lower())
            )

        if sources:
            query = query.where(Tag.source.in_(sources))

        # Apply limit
        query = query.limit(limit)

        # Execute query
        try:
            logger.info(f"Executing query for tags for persona {persona_id} with document_set_ids: {document_set_ids}...")
            db_tags = db_session.execute(query).scalars().all()
            logger.info(f"Found {len(db_tags)} tags for persona {persona_id}")

            # Convert to response format
            server_tags = [
                SourceTag(
                    tag_key=db_tag.tag_key, tag_value=db_tag.tag_value, source=db_tag.source
                )
                for db_tag in db_tags
            ]
            logger.info(f"Returning {len(server_tags)} tags for persona {persona_id}")

            return TagResponse(tags=server_tags)
        except Exception as e:
            logger.error(f"Error executing query for persona {persona_id}: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Error fetching tags: {str(e)}"
            )
    except Exception as e:
        logger.error(f"Unexpected error in get_tags_for_persona: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching tags: {str(e)}"
        )
