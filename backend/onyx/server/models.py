from typing import Generic
from typing import Op<PERSON>
from typing import TypeVar
from uuid import <PERSON>UI<PERSON>

from pydantic import BaseModel
from typing import List

from onyx.auth.schemas import UserRole
from onyx.db.models import User


DataT = TypeVar("DataT")


class StatusResponse(BaseModel, Generic[DataT]):
    success: bool
    message: Optional[str] = None
    data: Optional[DataT] = None


class ApiKey(BaseModel):
    api_key: str


class IdReturn(BaseModel):
    id: int


class MinimalUserSnapshot(BaseModel):
    id: UUID
    email: str

class UserIdSnapshot(BaseModel):
    id: UUID

class FullUserSnapshot(BaseModel):
    id: UUID
    email: str
    role: UserRole
    is_active: bool
    password_configured: bool
    status: str  # "active", "inactive", "pending_assignment", "ready_to_signup" - computed dynamically

    @classmethod
    def from_user_model(cls, user: User, db_session=None) -> "FullUserSnapshot":
        # Status is computed dynamically from user state and team assignments
        return cls(
            id=user.id,
            email=user.email,
            role=user.role,
            is_active=user.is_active,
            password_configured=user.password_configured,
            status=user.status,  # Dynamic property that considers role, is_active, and team assignments
        )


class InvitedUserSnapshot(BaseModel):
    email: str
    role: UserRole
    status: str | None = None
    user_id: str | None = None


class DisplayPriorityRequest(BaseModel):
    display_priority_map: dict[int, int]

class BulkInviteUserRequest(BaseModel):
    users: List[InvitedUserSnapshot]
