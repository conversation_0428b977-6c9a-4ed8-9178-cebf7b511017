"""
User status definitions and utilities.
"""

from enum import Enum
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from onyx.db.models import User


class UserStatus(str, Enum):
    """
    User status enum for type safety and consistency.

    Workflow: Invite → Assign Team → Signup → Active
    """
    ACTIVE = "active"                    # User can login and access the system
    INACTIVE = "inactive"                # User account is deactivated (manually set)
    READY_TO_SIGNUP = "ready_to_signup"  # User can complete signup process
    PENDING_ASSIGNMENT = "pending_assignment"  # User needs team assignment before signup/login


class UserStatusChecker:
    """Utility class for status-related operations."""
    
    @staticmethod
    def can_signup(status: UserStatus) -> bool:
        """Check if user can complete signup process."""
        return status == UserStatus.READY_TO_SIGNUP
    
    @staticmethod
    def can_login(status: UserStatus) -> bool:
        """Check if user can login to the system."""
        return status == UserStatus.ACTIVE
    
    @staticmethod
    def needs_team_assignment(status: UserStatus) -> bool:
        """Check if user needs team assignment."""
        return status == UserStatus.PENDING_ASSIGNMENT
    
    @staticmethod
    def get_signup_error_message(status: UserStatus) -> str:
        """Get appropriate error message for signup attempts."""
        if status == UserStatus.PENDING_ASSIGNMENT:
            return "You must be assigned to a team before you can sign up. Please contact your administrator."
        elif status == UserStatus.ACTIVE:
            return "You have already signed up and can login directly."
        else:
            return "You are not eligible to sign up yet. Please contact Admin."
    
    @staticmethod
    def get_login_error_message(status: UserStatus) -> str:
        """Get appropriate error message for login attempts."""
        if status == UserStatus.PENDING_ASSIGNMENT:
            return "You must be assigned to a team to login. Please contact your administrator."
        elif status == UserStatus.READY_TO_SIGNUP:
            return "Please complete your signup process first."
        else:
            return "You are not allowed to login. Please contact your administrator."


def compute_user_status(user: "User") -> UserStatus:
    """
    Compute user status based on current state.

    This function can be used independently of the User model
    for testing or other purposes.
    """
    from onyx.auth.schemas import UserRole

    # Admin users don't need team assignments
    if user.role == UserRole.ADMIN:
        return UserStatus.ACTIVE if user.is_active else UserStatus.READY_TO_SIGNUP

    # Non-admin users MUST have team assignments
    has_teams = len(user.user_team_ids) > 0

    if not has_teams:
        # No teams = always need team assignment first (regardless of is_active)
        return UserStatus.PENDING_ASSIGNMENT

    # Has teams
    if user.is_active:
        return UserStatus.ACTIVE  # Can login and use system
    else:
        return UserStatus.READY_TO_SIGNUP  # Can complete signup process


async def compute_user_status_async(db_session, user_id: str, user_role: "UserRole", is_active: bool) -> UserStatus:
    """
    Compute user status asynchronously by querying team count directly.
    This avoids the relationship loading issue.
    """
    from onyx.auth.schemas import UserRole
    from onyx.db.models import User__UserGroup, InvitedUser
    from sqlalchemy import select, func

    # Count teams for the user
    team_count_result = await db_session.execute(
        select(func.count(User__UserGroup.user_group_id))
        .where(User__UserGroup.user_id == user_id)
    )
    team_count = team_count_result.scalar() or 0

    # If user is active, determine status based on role and teams
    if is_active:
        if user_role == UserRole.ADMIN:
            return UserStatus.ACTIVE  # Admins don't need teams

        # Non-admin users need teams to be active
        return UserStatus.ACTIVE if team_count > 0 else UserStatus.PENDING_ASSIGNMENT

    # User is not active - check if they're in signup flow or deactivated
    try:
        # Check if user has an InvitedUser record (signup flow)
        invited_user_result = await db_session.execute(
            select(InvitedUser).where(InvitedUser.id == user_id)
        )
        invited_user = invited_user_result.scalar_one_or_none()

        if invited_user:
            # User is in signup flow
            if user_role == UserRole.ADMIN:
                return UserStatus.READY_TO_SIGNUP  # Admins can signup immediately
            else:
                # Non-admin users need teams to signup
                return UserStatus.READY_TO_SIGNUP if team_count > 0 else UserStatus.PENDING_ASSIGNMENT
        else:
            # No InvitedUser record - this is a deactivated user
            return UserStatus.INACTIVE
    except Exception:
        # Conservative fallback for any errors
        return UserStatus.INACTIVE


def require_status(*required_statuses: UserStatus):
    """
    Decorator to require specific user status for endpoint access.

    Usage:
        @require_status(UserStatus.ACTIVE)
        def some_endpoint(user: User = Depends(current_user)):
            # Only active users can access this endpoint
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Find user parameter
            user = None
            for arg in args:
                if hasattr(arg, 'status_enum'):
                    user = arg
                    break

            if not user:
                # Look in kwargs
                for value in kwargs.values():
                    if hasattr(value, 'status_enum'):
                        user = value
                        break

            if user:
                user_status = user.status_enum
                if user_status not in required_statuses:
                    from fastapi import HTTPException
                    raise HTTPException(
                        status_code=403,
                        detail=f"Access denied. Required status: {[s.value for s in required_statuses]}, current: {user_status.value}"
                    )

            return func(*args, **kwargs)
        return wrapper
    return decorator
