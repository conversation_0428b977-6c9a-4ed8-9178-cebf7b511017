---
- name: Deploy to "{{target}}" Env
  hosts: "{{target}}"
  tasks:
    - name: Environment
      debug:
        msg: "Hi, {{ ansible_hostname }} - {{ ansible_distribution }} {{ ansible_distribution_version }}"
    
    - name: deploy service to finnate
      shell: bash deploy.sh
      args:
        chdir: ~/athena/
      environment:
        NEW_TAG: "{{ lookup('env', 'CI_COMMIT_TAG') }}"
      register: results

    - name: output
      debug:
        msg: "{{ results.stdout }}"
